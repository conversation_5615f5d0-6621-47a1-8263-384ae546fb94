import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';

class SearchTabbar extends StatefulWidget {
  const SearchTabbar({super.key});

  // @override
  // Size get preferredSize =>
  //     Size.fromHeight(LayoutConfig.bottomNavBarHeight - 3.0);

  @override
  SearchTabbarState createState() => SearchTabbarState();
}

class SearchTabbarState extends State<SearchTabbar>
    with SingleTickerProviderStateMixin {
  final double _screenXPadding = TabbarConfig.screenXPadding;

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.sizeOf(context).width;
    final double containerWidth = screenWidth - _screenXPadding;

    double tabWidth = containerWidth / 5;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: _screenXPadding),
      color: context.colors.scaffoldColor,
      child: TabBar(
        isScrollable: true,
        padding: EdgeInsets.zero,
        dividerHeight: 0.0,
        indicatorPadding: EdgeInsets.only(bottom: -1.0),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(width: 2.7, color: context.colors.brandColor),
          borderRadius: BorderRadius.zero, // This removes the rounded corners
        ),
        labelColor: context.colors.labelColor,
        labelPadding: const EdgeInsets.symmetric(horizontal: 8.0),
        unselectedLabelColor: context.colors.primarySwatch[300],
        tabs: [
          SizedBox(
            width: tabWidth,
            child: const Tab(text: 'Photos'),
          ),
          SizedBox(
            width: tabWidth,
            child: const Tab(text: 'Categories'),
          ),
          SizedBox(
            width: tabWidth,
            child: const Tab(text: 'Cameras'),
          ),
          SizedBox(
            width: tabWidth,
            child: const Tab(text: 'Artists'),
          ),
        ],
      ),
    );
  }
}
